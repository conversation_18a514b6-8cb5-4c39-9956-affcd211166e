//
//  VideoListController.swift
//  DouYinSwift5
//
//  Created by lym on 2020/7/28.
//  Copyright © 2020 lym. All rights reserved.
//

import UIKit

enum VideoListType {
    case works
    case like
    case collect
}

class VideoListController: BaseViewController {
    fileprivate var didScroll: ((UIScrollView) -> Void)?
    private let type: VideoListType
    private let userId: String
    private var dataSource: [VideoItem] = []
    private let LINE_SPACE: CGFloat = 4
    private let ITEM_SPACE: CGFloat = 1
    private var itemWidth: CGFloat {
        return (view.width - 20) / 3 - ITEM_SPACE * 2
    }
    private var itemHeight: CGFloat {
        return itemWidth * (16.0 / 9.0)
    }
    private let VideoListCellId = "VideoListCellId"
    private var collectionView: UICollectionView!

    // MARK: - 空数据占位视图
    private lazy var emptyPlaceholderView: UIView = {
        let container = UIView()
        container.backgroundColor = .clear
        container.isHidden = true

        let imageView = UIImageView(image: UIImage(named: "empty_data_placeholder_image"))
        imageView.contentMode = .scaleAspectFit
        imageView.translatesAutoresizingMaskIntoConstraints = false
        container.addSubview(imageView)

        let label = UILabel()
        label.textColor = UIColor(hex: "#999999")
        label.font = .systemFont(ofSize: 14)
        label.textAlignment = .center
        label.translatesAutoresizingMaskIntoConstraints = false
        container.addSubview(label)

        // 根据类型设置不同的提示文字
        switch type {
        case .works:
            label.text = "暂无作品"
        case .like:
            label.text = "暂无喜欢的作品"
        case .collect:
            label.text = "暂无收藏的作品"
        }

        // 设置约束
        NSLayoutConstraint.activate([
            imageView.centerXAnchor.constraint(equalTo: container.centerXAnchor),
            imageView.centerYAnchor.constraint(equalTo: container.centerYAnchor, constant: -20),
            imageView.widthAnchor.constraint(equalToConstant: 120),
            imageView.heightAnchor.constraint(equalToConstant: 120),

            label.topAnchor.constraint(equalTo: imageView.bottomAnchor, constant: 12),
            label.centerXAnchor.constraint(equalTo: container.centerXAnchor),
            label.leadingAnchor.constraint(greaterThanOrEqualTo: container.leadingAnchor, constant: 20),
            label.trailingAnchor.constraint(lessThanOrEqualTo: container.trailingAnchor, constant: -20)
        ])

        return container
    }()

    init(type: VideoListType, userId: String) {
        self.type = type
        self.userId = userId
        super.init(nibName: nil, bundle: nil)
        print("=== VideoListController 初始化 ===")
        print("类型: \(type), 用户ID: \(userId)")
        print("实例地址: \(Unmanaged.passUnretained(self).toOpaque())")
        print("===============================")
    }

    required init?(coder: NSCoder) { fatalError("init(coder:) has not been implemented") }

    override func viewDidLoad() {
        super.viewDidLoad()
        
        showNavBar = false

        let layout = UICollectionViewFlowLayout()
        layout.minimumLineSpacing = LINE_SPACE
        layout.minimumInteritemSpacing = ITEM_SPACE
        layout.itemSize = CGSize(width: itemWidth, height: itemHeight)
        collectionView = UICollectionView(frame: CGRect.zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .clear
        collectionView.alwaysBounceVertical = true
        
        // 永久关闭滚动条显示
        collectionView.showsVerticalScrollIndicator = false
        collectionView.showsHorizontalScrollIndicator = false

        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(VideoViewCell.self, forCellWithReuseIdentifier: VideoListCellId)
        view.addSubview(collectionView)

        // 添加空数据占位视图
        view.addSubview(emptyPlaceholderView)

        collectionView.translatesAutoresizingMaskIntoConstraints = false
        collectionView.leftAnchor.constraint(equalTo: view.leftAnchor, constant: 8).isActive = true
        collectionView.topAnchor.constraint(equalTo: view.topAnchor, constant: 8).isActive = true
        collectionView.rightAnchor.constraint(equalTo: view.rightAnchor, constant: -8).isActive = true
        collectionView.bottomAnchor.constraint(equalTo: view.bottomAnchor).isActive = true

        // 设置空数据占位视图约束
        emptyPlaceholderView.translatesAutoresizingMaskIntoConstraints = false
        emptyPlaceholderView.leftAnchor.constraint(equalTo: view.leftAnchor).isActive = true
        emptyPlaceholderView.topAnchor.constraint(equalTo: view.topAnchor).isActive = true
        emptyPlaceholderView.rightAnchor.constraint(equalTo: view.rightAnchor).isActive = true
        emptyPlaceholderView.bottomAnchor.constraint(equalTo: view.bottomAnchor).isActive = true

        if #available(iOS 11.0, *) {
            collectionView.contentInsetAdjustmentBehavior = .never
        } else {
            automaticallyAdjustsScrollViewInsets = false
        }

        loadData()
    }

    // MARK: - 更新空数据占位视图显示状态
    private func updateEmptyPlaceholderVisibility() {
        let isEmpty = dataSource.isEmpty
        emptyPlaceholderView.isHidden = !isEmpty
        collectionView.isHidden = isEmpty
    }

    private func loadData() {
        switch type {
        case .works:
            APIManager.shared.getPersonHomeWorks(customerId: userId, page: 0, size: 20) { [weak self] result in
                self?.handleResult(result)
            }
        case .like:
            APIManager.shared.getPersonHomeWorksLike(customerId: userId, page: 0, size: 20) { [weak self] result in
                self?.handleResult(result)
            }
        case .collect:
            APIManager.shared.doWorksLikeAndCollect(customerId: userId, page: 0, size: 20) { [weak self] result in
                self?.handleResult(result)
            }
        }
    }

   private func handleResult(_ result: Result<VideoCollectionListResponse, APIError>) {
        switch result {
        case .success(let response):
            // 检查是否为私密账号（只在作品tab时处理）
            if response.status == 300 {
                // 显示私密账号提示
                if let message = response.msg, !message.isEmpty {
                    showToast(message)
                }
                // 只有在作品tab时才通知父控制器隐藏统计数据
                if type == .works {
                    NotificationCenter.default.post(
                        name: NSNotification.Name("HideStatsForPrivateAccount"),
                        object: nil
                    )
                }
                // 清空数据源
                self.dataSource = []

                // 通知父控制器更新对应tab的数量为0
                notifyParentWithActualCount(0)
            } else {
                print("=== VideoListController 数据加载完成 ===")
                print("实例地址: \(Unmanaged.passUnretained(self).toOpaque())")
                print("类型: \(type), 总数: \(response.data?.total ?? 0), 列表数量: \(response.data?.list?.count ?? 0)")

                self.dataSource = response.data?.list ?? []

                // 打印前几个数据项
                for (index, item) in self.dataSource.prefix(min(5, self.dataSource.count)).enumerated() {
                    print("数据[\(index)]: ID=\(item.id ?? -1), 标题=\(item.worksTitle ?? "无标题")")
                }
                print("=====================================")

                // 只有在作品tab时才通知父控制器显示统计数据（恢复正常状态）
                if type == .works {
                    NotificationCenter.default.post(
                        name: NSNotification.Name("ShowStatsForPublicAccount"),
                        object: nil
                    )
                }

                // 通知父控制器更新对应tab的实际数量
                notifyParentWithActualCount(self.dataSource.count)
            }
            self.collectionView.reloadData()
            // 更新空数据占位视图显示状态
            self.updateEmptyPlaceholderVisibility()
        case .failure(let error):
            print("加载视频列表失败: \(error)")
            // 失败时也通知父控制器更新数量为0
            notifyParentWithActualCount(0)
            // 失败时也更新空数据占位视图显示状态
            self.updateEmptyPlaceholderVisibility()
        }
    }

    /// 通知父控制器更新对应tab的实际数量
    private func notifyParentWithActualCount(_ count: Int) {
        let notificationName: String
        switch type {
        case .works:
            notificationName = "UpdateWorksCount"
        case .like:
            notificationName = "UpdateLikeCount"
        case .collect:
            notificationName = "UpdateCollectCount"
        }

        NotificationCenter.default.post(
            name: NSNotification.Name(notificationName),
            object: count
        )

        print("通知父控制器更新\(type)数量: \(count)")
    }

    // MARK: - Toast提示
    private func showToast(_ message: String) {
        // 获取最顶层的窗口视图
        guard let keyWindow = UIApplication.shared.windows.first(where: { $0.isKeyWindow }) else {
            return
        }

        // 创建容器视图
        let containerView = UIView()
        containerView.backgroundColor = UIColor.black.withAlphaComponent(0.8)
        containerView.layer.cornerRadius = 8
        containerView.translatesAutoresizingMaskIntoConstraints = false

        // 创建消息标签
        let messageLabel = UILabel()
        messageLabel.text = message
        messageLabel.textColor = .white
        messageLabel.numberOfLines = 0
        messageLabel.textAlignment = .center
        messageLabel.font = .systemFont(ofSize: 14)
        messageLabel.translatesAutoresizingMaskIntoConstraints = false

        // 添加视图层级
        containerView.addSubview(messageLabel)
        keyWindow.addSubview(containerView)

        // 设置约束
        NSLayoutConstraint.activate([
            messageLabel.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 12),
            messageLabel.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -12),
            messageLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 16),
            messageLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -16),

            containerView.centerXAnchor.constraint(equalTo: keyWindow.centerXAnchor),
            containerView.centerYAnchor.constraint(equalTo: keyWindow.centerYAnchor),
            containerView.widthAnchor.constraint(lessThanOrEqualToConstant: 280)
        ])

        // 显示动画
        containerView.alpha = 0
        UIView.animate(withDuration: 0.3, animations: {
            containerView.alpha = 1
        }) { _ in
            // 2秒后隐藏
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                UIView.animate(withDuration: 0.3, animations: {
                    containerView.alpha = 0
                }) { _ in
                    containerView.removeFromSuperview()
                }
            }
        }
    }
}

extension VideoListController: UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        // 点击作品 / 喜欢 / 收藏列表中的视频，跳转到播放中心
        print("=== VideoListController 点击事件 ===")
        print("点击索引: \(indexPath.row)")
        print("数据源总数: \(dataSource.count)")

        // 检查索引是否越界
        guard indexPath.row < dataSource.count else {
            print("❌ 索引越界: \(indexPath.row) >= \(dataSource.count)")
            return
        }

        let selectedItem = dataSource[indexPath.row]
        print("选中的视频: ID=\(selectedItem.id ?? -1), 标题=\(selectedItem.worksTitle ?? "无标题")")

        // 打印前几个数据项用于对比
        for (index, item) in dataSource.prefix(min(5, dataSource.count)).enumerated() {
            let marker = index == indexPath.row ? "👉" : "  "
            print("\(marker) 索引[\(index)]: ID=\(item.id ?? -1), 标题=\(item.worksTitle ?? "无标题")")
        }

        let playerVC = VideoDisplayCenterViewController(
            videoList: dataSource,
            startIndex: indexPath.row,
            hideNavBackButton: false,
            showCustomNavBar: true,
            needsTabBarOffset: false
        )
        print("创建播放器控制器，传入索引: \(indexPath.row)")
        print("===========================")

        self.navigationController?.pushViewController(playerVC, animated: true)
    }

    func collectionView(_: UICollectionView, shouldSelectItemAt _: IndexPath) -> Bool {
        return true
    }

    func collectionView(_: UICollectionView, shouldHighlightItemAt _: IndexPath) -> Bool {
        return true
    }
}

extension VideoListController: UICollectionViewDataSource {
    func collectionView(_: UICollectionView, numberOfItemsInSection _: Int) -> Int {
        return dataSource.count
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: VideoListCellId, for: indexPath) as! VideoViewCell
        let item = dataSource[indexPath.row]
        print("cell index: \(indexPath.row), id: \(item.id ?? -1), title: \(item.worksTitle ?? "nil"), cover: \(item.worksCoverImg ?? "nil")")
        cell.bind(item: item)
        return cell
    }
}

// MARK: - PageContainScrollView

extension VideoListController: UIScrollViewDelegate {
    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        didScroll?(scrollView)
    }
}

extension VideoListController: PageContainScrollView {
    func scrollView() -> UIScrollView {
        return collectionView
    }

    func scrollViewDidScroll(callBack: @escaping (UIScrollView) -> Void) {
        didScroll = callBack
    }
}
